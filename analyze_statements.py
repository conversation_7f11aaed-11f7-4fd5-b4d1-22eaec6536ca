#!/usr/bin/env python3

import pdfplumber
import os

def analyze_statement(file_path, name):
    print(f"\n{'='*60}")
    print(f"ANALYZING {name}")
    print(f"{'='*60}")
    
    with pdfplumber.open(file_path) as pdf:
        full_text = ''
        for page in pdf.pages:
            if page.extract_text():
                full_text += page.extract_text() + '\n'
    
    lines = full_text.split('\n')
    
    print(f"Total lines: {len(lines)}")
    print(f"Total characters: {len(full_text)}")
    
    # Look for key markers
    markers = [
        "Posted Transactions",
        "Transaction Date",
        "Posting Date", 
        "Description",
        "Amount",
        "Date Description Category Amount",
        "Date Description Amount",
        "Activity since last statement",
        "Transactions"
    ]
    
    print("\nKey markers found:")
    for marker in markers:
        for i, line in enumerate(lines):
            if marker in line:
                print(f"  '{marker}' at line {i}: {repr(line)}")
                break
    
    # Look for transaction patterns
    print("\nLooking for transaction patterns...")
    
    # Find lines that look like transactions
    import re
    date_patterns = [
        r'\b\d{2}/\d{2}/\d{2}\b',  # MM/DD/YY
        r'\b\w{3} \d{1,2}, \d{4}\b',  # Jul 16, 2025
    ]
    amount_pattern = r'-?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?'
    
    transaction_lines = []
    for i, line in enumerate(lines):
        for date_pattern in date_patterns:
            if re.search(date_pattern, line) and re.search(amount_pattern, line):
                transaction_lines.append((i, line))
                break
    
    print(f"Found {len(transaction_lines)} potential transaction lines:")
    for i, (line_num, line) in enumerate(transaction_lines[:10]):  # Show first 10
        print(f"  Line {line_num}: {repr(line)}")
    
    if len(transaction_lines) > 10:
        print(f"  ... and {len(transaction_lines) - 10} more")
    
    # Show context around transaction section
    for marker in ["Posted Transactions", "Date Description Category Amount", "Date Description Amount"]:
        for i, line in enumerate(lines):
            if marker in line:
                print(f"\nContext around '{marker}' (lines {max(0, i-2)} to {min(len(lines), i+15)}):")
                for j in range(max(0, i-2), min(len(lines), i+15)):
                    prefix = ">>> " if j == i else "    "
                    print(f"{prefix}{j:3d}: {repr(lines[j])}")
                break

if __name__ == "__main__":
    # Analyze Wells Fargo statement
    wf_path = 'Statements/Account Detail - Wells Fargo.pdf'
    if os.path.exists(wf_path):
        analyze_statement(wf_path, "WELLS FARGO")
    
    # Analyze Chase statement
    chase_path = 'Statements/Sapphire Reserve (...4846) - chase.com.pdf'
    if os.path.exists(chase_path):
        analyze_statement(chase_path, "CHASE")
