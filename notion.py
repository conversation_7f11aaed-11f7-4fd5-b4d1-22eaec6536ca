import os
from dotenv import load_dotenv
from notion_client import Client
from tabulate import tabulate

class Notion:
    def __init__(self):
        load_dotenv()
        self.client = Client(auth=os.environ["NOTION_SECRET"])
        self.database_id = os.environ["NOTION_DATABASE_ID"]
        
        # Initialize expense types
        self.expense_types = {
            'GROCERY': os.environ["NOTION_EXPENSE_TYPE_GROCERY"],
            'UTILITY': os.environ["NOTION_EXPENSE_TYPE_UTILITY"],
            'RESTAURANT': os.environ["NOTION_EXPENSE_TYPE_RESTAURANT"],
            'HOUSING': os.environ["NOTION_EXPENSE_TYPE_HOUSING"],
            'TRANSPORTATION': os.environ["NOTION_EXPENSE_TYPE_TRANSPORTATION"],
            'HEALTHCARE': os.environ["NOTION_EXPENSE_TYPE_HEALTHCARE"],
            'ENTERTAINMENT': os.environ["NOTION_EXPENSE_TYPE_ENTERTAINMENT"],
            'INSURANCE': os.environ["NOTION_EXPENSE_TYPE_INSURANCE"],
            'TRAVEL': os.environ["NOTION_EXPENSE_TYPE_TRAVEL"],
            'SOCIAL': os.environ["NOTION_EXPENSE_TYPE_SOCIAL"],
            'SUBSCRIPTION': os.environ["NOTION_EXPENSE_TYPE_SUBSCRIPTION"],
            'OTHER': os.environ["NOTION_EXPENSE_TYPE_OTHER"],
            'PET': os.environ["NOTION_EXPENSE_TYPE_PET"]
        }
        
        # Initialize credit cards
        self.credit_cards = {
            'AMAZON': os.environ["NOTION_CC_AMAZON"],
            'BILT': os.environ["NOTION_CC_BILT"],
            'SAPPHIRE': os.environ["NOTION_CC_SAPPHIRE"],
            'DEBIT': os.environ["NOTION_CC_DEBIT"]
        }

    def get_database(self):
        database = self.client.databases.query(database_id=self.database_id)
        return database

    def get_database_columns(self):
        # Retrieve the database schema
        database = self.client.databases.retrieve(database_id=self.database_id)
        properties = database["properties"]
        columns = {name: details["type"] for name, details in properties.items()}
        return columns

    def get_database_filtered_by_month(self, year: int, month: int):
        # Format the start and end dates for the filter
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"

        # Query the database with a filter for the Date column
        response = self.client.databases.query(
            database_id=self.database_id,
            filter={
                "and": [
                    {
                        "property": "Date",
                        "date": {
                            "on_or_after": start_date
                        }
                    },
                    {
                        "property": "Date",
                        "date": {
                            "before": end_date
                        }
                    }
                ]
            }
        )
        return response["results"]


    def parse_chase_category_to_notion_expense_type(self, category: str):
        category_lower = category.lower()
        if "groceries" in category_lower:
            return self.expense_types['GROCERY']
        elif "utilities" in category_lower:
            return self.expense_types['UTILITY']
        elif "restaurants" in category_lower:
            return self.expense_types['RESTAURANT']
        elif "housing" in category_lower:
            return self.expense_types['HOUSING']
        elif "transportation" in category_lower:
            return self.expense_types['TRANSPORTATION']
        elif "healthcare" in category_lower:
            return self.expense_types['HEALTHCARE']
        elif "entertainment" in category_lower:
            return self.expense_types['ENTERTAINMENT']
        elif "insurance" in category_lower:
            return self.expense_types['INSURANCE']
        elif "travel" in category_lower:
            return self.expense_types['TRAVEL']
        elif "social" in category_lower:
            return self.expense_types['SOCIAL']
        elif "subscription" in category_lower:
            return self.expense_types['SUBSCRIPTION']
        elif "pet" in category_lower:
            return self.expense_types['PET']
        else:
            return self.expense_types['OTHER']


    def get_credit_card_id(self, credit_card_name: str):
        if credit_card_name == "Amazon":
            return self.credit_cards['AMAZON']
        elif credit_card_name == "Bilt":
            return self.credit_cards['BILT']
        else:
            return self.credit_cards['DEBIT']


    def add_example_row(self):
        insurance_relation_id = self.expense_types['INSURANCE']

        # Add a simple example row to the database
        response = self.client.pages.create(
            parent={"database_id": self.database_id},
            properties={
                "Expense Record": {
                    "title": [
                        {
                            "text": {
                                "content": "Example Expense"
                            }
                        }
                    ]
                },
                "Date": {
                    "date": {
                        "start": "2025-04-01"
                    }
                },
                "API Key": {
                    "rich_text": [
                        {
                            "text": {
                                "content": "2025-04-01Example Expense100.00"
                            }
                        }
                    ]
                },
                "Amount": {
                    "number": 100.50
                },
                "Status": {
                    "select": {
                        "name": "Pending"
                    }
                },
                "Note": {
                    "rich_text": [
                        {
                            "text": {
                                "content": "This is a test entry."
                            }
                        }
                    ]
                },
                "Expense Type": {
                    "relation": [
                        {
                            "id": insurance_relation_id
                        }
                    ]
                }
            }
        )
        return response


    def add_row(self, date, name, amount, category, credit_card_id):
        # Parse the category to an expense type
        expense_type = self.parse_chase_category_to_notion_expense_type(category)

        # Clean amount - remove '$' and convert to float
        cleaned_amount = float(amount.replace('$', '').replace(',', ''))

        # Create API key
        api_key = f"{date}{name}{amount}"

        response = self.client.pages.create(
            parent={"database_id": self.database_id},
            properties={
                "Expense Record": {
                    "title": [
                        {
                            "text": {
                                "content": name
                            }
                        }
                    ]
                },
                "Date": {
                    "date": {
                        "start": date
                    }
                },
                "Amount": {
                    "number": cleaned_amount
                },
                "Status": {
                    "select": {
                        "name": "Unpaid"
                    }
                },
                "Note": {
                    "rich_text": [
                        {
                            "text": {
                                "content": "SCRIPT"
                            }
                        }
                    ]
                },
                "Expense Type": {
                    "relation": [
                        {
                            "id": expense_type
                        }
                    ]
                },
                "Credit Card": {
                    "relation": [
                        {
                            "id": credit_card_id
                        }
                    ]
                },
                "API Key": {
                    "rich_text": [
                        {
                            "text": {
                                "content": api_key
                            }
                        }
                    ]
                }
            }
        )
        return response

    def prettify_rows(self, rows):
        """
        Convert rows to a string table for debugging
        """

        # Extract relevant data from rows
        table_data = []
        for row in rows:
            properties = row["properties"]
            table_data.append([
                properties["Expense Record"]["title"][0]["text"]["content"] if properties["Expense Record"]["title"] else "",
                properties["Date"]["date"]["start"] if properties["Date"]["date"] else "",
                properties["Amount"]["number"] if "number" in properties["Amount"] else "",
                properties["Status"]["select"]["name"] if properties["Status"]["select"] else "",
                properties["Note"]["rich_text"][0]["text"]["content"] if properties["Note"]["rich_text"] else ""
            ])
        
        # Define table headers
        headers = ["Expense Record", "Date", "Amount", "Status", "Note"]
        return tabulate(table_data, headers=headers, tablefmt="grid")

    def get_api_key(self, row):
        """
        Get the API key column from a row
        or construct it for older rows (pre-2025-04-01)
        """
        properties = row["properties"]
        api_key_prop = properties.get("API Key", {}).get("rich_text", [])
        if api_key_prop and api_key_prop[0].get("text", {}).get("content"):
            return api_key_prop[0]["text"]["content"]
        return f"{properties['Date']['date']['start']}{properties['Expense Record']['title'][0]['text']['content']}{properties['Amount']['number']}"
