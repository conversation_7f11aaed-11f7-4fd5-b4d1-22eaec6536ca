import os
import utils

if __name__ == "__main__":
    root_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_path = os.path.join(root_dir, "Statements")
    print("pdf_path: ", pdf_path)
    files = utils.get_files_in_dir(pdf_path)
    print("files: ", files)
    rows = []
    for file in files:
        text = utils.extract_text__pymupdf(file)
        statement_text = utils.strip_to_statement__chase(text)
        rows += utils.parse_statement(statement_text)
    
    print("rows: ", rows)
