import os
import utils

if __name__ == "__main__":
    root_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_path = os.path.join(root_dir, "Statements")
    print("pdf_path: ", pdf_path)
    files = utils.get_files_in_dir(pdf_path)
    print("files: ", files)

    all_transactions = []

    for file in files:
        print(f"\nProcessing file: {file}")

        # Try different text extraction methods
        try:
            text = utils.extract_text__pdfplumber(file)
            print(f"Extracted {len(text)} characters using pdfplumber")
        except Exception as e:
            print(f"pdfplumber failed: {e}")
            try:
                text = utils.extract_text__pdfminer(file)
                print(f"Extracted {len(text)} characters using pdfminer")
            except Exception as e2:
                print(f"pdfminer also failed: {e2}")
                continue

        # Detect statement type and strip to relevant section
        statement_type = utils.detect_statement_type(text)
        print(f"Detected statement type: {statement_type}")

        statement_text = utils.strip_to_statement(text)
        print(f"Statement section length: {len(statement_text)}")

        # Parse transactions
        transactions = utils.parse_statement(statement_text)
        print(f"Found {len(transactions)} transactions")

        all_transactions.extend(transactions)

    print(f"\nTotal transactions found: {len(all_transactions)}")
    print("\nSample transactions:")
    for i, (date, desc, amount, category) in enumerate(all_transactions[:10]):
        print(f"  {i+1}. {date} | {desc} | {amount} | {category}")

    if len(all_transactions) > 10:
        print(f"  ... and {len(all_transactions) - 10} more transactions")
