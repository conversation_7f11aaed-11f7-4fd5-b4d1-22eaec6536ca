import fitz  # pymupdf
import pdfplumber
from io import StringIO
from pdfminer.converter import TextConverter
from pdfminer.layout import LAParams
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfparser import PDFParser
import os
import re
from datetime import datetime

INITIAL_LAST_UPDATE = "2025-02-15"
DATE_FORMAT = "%Y-%m-%d"

def parse_statement__chase__opera(statement_text: str):
    """
    Parse Chase statements from Opera browser into a list of transactions with categories
    """
    lines = statement_text.split("\n")
    transactions = []
    date_pattern = re.compile(r"\b\w{3} \d{1,2}, \d{4}\b")
    amount_pattern = re.compile(r"-?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?")
    categories = ["Shopping", "Food & drink", "Groceries", "Bills & utilities", "Home", "—"]
    
    # First, let's identify where the transaction table starts
    table_start = -1
    for i, line in enumerate(lines):
        if "Date" in line and ("Description" in line or "Category" in line or "Amount" in line):
            table_start = i
            break
    
    if table_start == -1:
        return []
    
    # Process the lines after the header
    i = table_start + 1
    current_date = None
    current_desc = []
    current_amount = None
    current_category = None
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line or "You've reached the end" in line:
            i += 1
            continue
        
        # Check for date
        date_match = date_pattern.search(line)
        if date_match:
            # Save previous transaction if complete
            if current_date and current_amount and current_desc:
                desc = " ".join(current_desc).strip()
                if desc:
                    transactions.append((
                        current_date,
                        desc,
                        current_amount,
                        current_category or "—"
                    ))
            
            # Start new transaction
            current_date = date_match.group()
            current_desc = []
            current_amount = None
            current_category = None
            
            # Process rest of line
            rest = line[date_match.end():].strip()
            if rest:
                # Check for amount in same line
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    desc = rest[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                else:
                    current_desc.append(rest)
        else:
            # Check for amount in line
            amount_match = amount_pattern.search(line)
            if amount_match:
                # If we already have an amount, this is a new transaction
                if current_amount and current_date:
                    desc = " ".join(current_desc).strip()
                    if desc:
                        transactions.append((
                            current_date,
                            desc,
                            current_amount,
                            current_category or "—"
                        ))
                    # Start new transaction with same date
                    current_desc = []
                    current_category = None
                
                current_amount = amount_match.group()
                desc = line[:amount_match.start()].strip()
                if desc:
                    current_desc.append(desc)
            # Check if line is a category
            elif line in categories:
                current_category = line
            # Otherwise add to description
            else:
                current_desc.append(line)
        
        i += 1
    
    # Add final transaction if complete
    if current_date and current_amount and current_desc:
        desc = " ".join(current_desc).strip()
        if desc:
            transactions.append((
                current_date,
                desc,
                current_amount,
                current_category or "—"
            ))
    
    # Clean up transactions
    cleaned = []
    for date, desc, amount, category in transactions:
        # Clean description
        desc = desc.replace("Category", "").strip()
        desc = re.sub(r'\s+', ' ', desc)
        
        # Remove category text if it appears in description
        for cat in categories:
            desc = desc.replace(cat, "").strip()
        
        # Remove "Pay Over Time eligible"
        desc = desc.replace("Pay Over Time eligible", "").strip()
        
        # Handle special cases
        if "Payment Thank You" in desc:
            category = "—"
        elif category == "—":
            if "Costco" in desc:
                category = "Shopping"
            elif "DoorDash" in desc or "Jack in the Box" in desc or "CURRY CLUB" in desc:
                category = "Food & drink"
            elif "Vons" in desc or "99 Ranch" in desc:
                category = "Groceries"
            elif "AT&T" in desc:
                category = "Bills & utilities"
            elif "IKEA" in desc:
                category = "Home"
        
        if desc:
            cleaned.append((date, desc, amount, category))
    
    return cleaned

def get_credit_card_name(text: str, notion: any):
    """
    Extracts the credit card name from the Chase statement header.
    Returns either 'Sapphire Reserve' or 'Amazon Chase' or None if not found.
    """
    if "Sapphire Reserve" in text:
        print('Found Sapphire Reserve')
        return notion.credit_cards["SAPPHIRE"]
    elif "Prime Visa" in text:
        print('Found Prime Visa')
        return notion.credit_cards["AMAZON"]
    elif "BILT" in text:
        print('Found BILT')
        return notion.credit_cards["BILT"]
    print("No credit card found. Defaulting to Sapphire.")
    return notion.credit_cards["SAPPHIRE"]

def strip_to_statement__chase(text: str):
    """
    Chase statements have a header that says "Activity since last statement"
    This function strips the text to the statement after that header
    """
    markers = [
        "Activity since last statement",
        "Date Description Category Amount",
        "Date Description Amount",
        "Transactions"
    ]
    
    for marker in markers:
        if marker in text:
            parts = text.split(marker, 1)
            if len(parts) > 1:
                return marker + parts[1]
    
    return ""

def strip_to_statement__wells_fargo(text: str):
    """
    Wells Fargo statements have a header that says "Posted transactions"
    This function strips the text to the statement after that header
    """
    # Look for the transaction section
    start_marker = "Posted Transactions"
    end_marker = "First Previous Next"
    
    print(f"Looking for '{start_marker}' in text...")
    if start_marker in text:
        print("Found start marker!")
        start_pos = text.find(start_marker)
        print(f"Start position: {start_pos}")
        
        if end_marker in text:
            print("Found end marker!")
            end_pos = text.find(end_marker, start_pos)
            print(f"End position: {end_pos}")
            
            if start_pos != -1 and end_pos != -1:
                # Extract the transaction section
                result = text[start_pos:end_pos].strip()
                print(f"Extracted text length: {len(result)}")
                return result
    
    print("No markers found or extraction failed")
    return ""

def extract_text__pdfplumber(file_path):
    """
    Extracts text from a PDF file using pdfplumber
    """
    with pdfplumber.open(file_path) as pdf:
        return "\n".join([page.extract_text() for page in pdf.pages if page.extract_text()])

def extract_text__pymupdf(file_path):
    """
    Extracts text from a PDF file using pymupdf
    """
    document = fitz.open(file_path)
    texts = ''
    for page_num in range(len(document)):
        page = document.load_page(page_num)
        text = page.get_text("text")
        texts += text
    document.close()
    print('texts: ', texts)
    return texts

def extract_text__pdfminer(file_path):
    """
    Extracts text from a PDF file using pdfminer.six with better layout preservation
    """
    output_string = StringIO()
    with open(file_path, 'rb') as in_file:
        parser = PDFParser(in_file)
        doc = PDFDocument(parser)
        rsrcmgr = PDFResourceManager()
        # Adjust LAParams for better table structure preservation
        device = TextConverter(rsrcmgr, output_string, laparams=LAParams(
            line_margin=0.5,  # Increased to better group related lines
            word_margin=0.1,
            boxes_flow=0.5,
            detect_vertical=True,
            all_texts=True,
            char_margin=2.0,  # Increased to better handle column spacing
            line_overlap=0.5,  # Added to better handle line spacing
        ))
        interpreter = PDFPageInterpreter(rsrcmgr, device)
        
        for page in PDFPage.create_pages(doc):
            interpreter.process_page(page)
            
    text = output_string.getvalue()
    output_string.close()
    
    # Debug print
    print("\nRaw text sample:")
    print(text[:200].replace('\n', '\\n'))
    
    return text

def get_files_in_dir(path: str): 
    files = []
    for file in os.listdir(path):
        if file.endswith(".pdf"):
            files.append(os.path.join(path, file))
    return files

def dynamic_date_parser(date: str):
    """
    Check date format and convert all dates to the format "YYYY-MM-DD"
    
    Args:
        date (str): Date string in one of these formats:
            - "MMM DD, YYYY" (e.g. "Feb 08, 2025")
            - "MM/DD/YY" (e.g. "04/01/25")
            - "YYYY-MM-DD" (e.g. "2025-04-01")
    
    Returns:
        str: Date in "YYYY-MM-DD" format
    """
    # If already in YYYY-MM-DD format, return as is
    if re.match(r"^\d{4}-\d{2}-\d{2}$", date):
        return date
    
    try:
        # Try MMM DD, YYYY format (e.g. "Feb 08, 2025")
        return datetime.strptime(date, "%b %d, %Y").strftime("%Y-%m-%d")
    except ValueError:
        try:
            # Try MM/DD/YY format (e.g. "04/01/25")
            return datetime.strptime(date, "%m/%d/%y").strftime("%Y-%m-%d")
        except ValueError:
            raise ValueError(f"Unsupported date format: {date}. Must be one of: 'MMM DD, YYYY', 'MM/DD/YY', or 'YYYY-MM-DD'")

def parse_statement__wells_fargo(statement_text: str):
    """
    Parses a Wells Fargo/BILT statement text into a list of transactions with categories0
    """
    lines = statement_text.split("\n")
    transactions = []
    date_pattern = re.compile(r"\b\d{2}/\d{2}/\d{2}\b")
    amount_pattern = re.compile(r"-?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?")
    
    # First, let's identify where the transaction table starts
    table_start = -1
    for i, line in enumerate(lines):
        if "Posted Transactions" in line:
            table_start = i
            break
    
    if table_start == -1:
        return []
    
    # Process the lines after the header
    i = table_start + 1
    current_date = None
    current_desc = []
    current_amount = None
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line or "First Previous Next" in line:
            i += 1
            continue
        
        # Check for date
        date_match = date_pattern.search(line)
        if date_match:
            # Save previous transaction if complete
            if current_date and current_amount and current_desc:
                desc = " ".join(current_desc).strip()
                if desc:
                    transactions.append((
                        current_date,
                        desc,
                        current_amount,
                        "—"  # Wells Fargo doesn't have categories
                    ))
            
            # Start new transaction
            current_date = date_match.group()
            current_desc = []
            current_amount = None
            
            # Process rest of line
            rest = line[date_match.end():].strip()
            if rest:
                # Check for amount in same line
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    desc = rest[:amount_match.start()].strip()
                    if desc:
                        # Remove the date from the description
                        desc = desc.replace(current_date, "").strip()
                        current_desc.append(desc)
                else:
                    # Remove the date from the description
                    desc = rest.replace(current_date, "").strip()
                    current_desc.append(desc)
        else:
            # Check for amount in line
            amount_match = amount_pattern.search(line)
            if amount_match:
                # If we already have an amount, this is a new transaction
                if current_amount and current_date:
                    desc = " ".join(current_desc).strip()
                    if desc:
                        transactions.append((
                            current_date,
                            desc,
                            current_amount,
                            "—"  # Wells Fargo doesn't have categories
                        ))
                    # Start new transaction with same date
                    current_desc = []
                
                current_amount = amount_match.group()
                desc = line[:amount_match.start()].strip()
                if desc:
                    current_desc.append(desc)
            # Otherwise add to description
            else:
                current_desc.append(line)
        
        i += 1
    
    # Add final transaction if complete
    if current_date and current_amount and current_desc:
        desc = " ".join(current_desc).strip()
        if desc:
            transactions.append((
                current_date,
                desc,
                current_amount,
                "—"  # Wells Fargo doesn't have categories
            ))
    
    return transactions
