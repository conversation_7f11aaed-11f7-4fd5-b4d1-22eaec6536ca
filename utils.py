import fitz  # pymupdf
import pdfplumber
from io import StringIO
from pdfminer.converter import TextConverter
from pdfminer.layout import LAParams
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfparser import PDFParser
import os
import re
from datetime import datetime

INITIAL_LAST_UPDATE = "2025-02-15"
DATE_FORMAT = "%Y-%m-%d"

def parse_statement__chase__opera(statement_text: str):
    """
    Parse Chase statements from Opera browser into a list of transactions with categories
    """
    lines = statement_text.split("\n")
    transactions = []
    date_pattern = re.compile(r"\b\w{3} \d{1,2}, \d{4}\b")
    amount_pattern = re.compile(r"-?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?")
    categories = ["Shopping", "Food & drink", "Groceries", "Bills & utilities", "Home", "—"]
    
    # First, let's identify where the transaction table starts
    table_start = -1
    for i, line in enumerate(lines):
        if "Date" in line and ("Description" in line or "Category" in line or "Amount" in line):
            table_start = i
            break
    
    if table_start == -1:
        return []
    
    # Process the lines after the header
    i = table_start + 1
    current_date = None
    current_desc = []
    current_amount = None
    current_category = None
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line or "You've reached the end" in line:
            i += 1
            continue
        
        # Check for date
        date_match = date_pattern.search(line)
        if date_match:
            # Save previous transaction if complete
            if current_date and current_amount and current_desc:
                desc = " ".join(current_desc).strip()
                if desc:
                    transactions.append((
                        current_date,
                        desc,
                        current_amount,
                        current_category or "—"
                    ))
            
            # Start new transaction
            current_date = date_match.group()
            current_desc = []
            current_amount = None
            current_category = None
            
            # Process rest of line
            rest = line[date_match.end():].strip()
            if rest:
                # Check for amount in same line
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    desc = rest[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                else:
                    current_desc.append(rest)
        else:
            # Check for amount in line
            amount_match = amount_pattern.search(line)
            if amount_match:
                # If we already have an amount, this is a new transaction
                if current_amount and current_date:
                    desc = " ".join(current_desc).strip()
                    if desc:
                        transactions.append((
                            current_date,
                            desc,
                            current_amount,
                            current_category or "—"
                        ))
                    # Start new transaction with same date
                    current_desc = []
                    current_category = None
                
                current_amount = amount_match.group()
                desc = line[:amount_match.start()].strip()
                if desc:
                    current_desc.append(desc)
            # Check if line is a category
            elif line in categories:
                current_category = line
            # Otherwise add to description
            else:
                current_desc.append(line)
        
        i += 1
    
    # Add final transaction if complete
    if current_date and current_amount and current_desc:
        desc = " ".join(current_desc).strip()
        if desc:
            transactions.append((
                current_date,
                desc,
                current_amount,
                current_category or "—"
            ))
    
    # Clean up transactions
    cleaned = []
    for date, desc, amount, category in transactions:
        # Clean description
        desc = desc.replace("Category", "").strip()
        desc = re.sub(r'\s+', ' ', desc)
        
        # Remove category text if it appears in description
        for cat in categories:
            desc = desc.replace(cat, "").strip()
        
        # Remove "Pay Over Time eligible"
        desc = desc.replace("Pay Over Time eligible", "").strip()
        
        # Handle special cases
        if "Payment Thank You" in desc:
            category = "—"
        elif category == "—":
            if "Costco" in desc:
                category = "Shopping"
            elif "DoorDash" in desc or "Jack in the Box" in desc or "CURRY CLUB" in desc:
                category = "Food & drink"
            elif "Vons" in desc or "99 Ranch" in desc:
                category = "Groceries"
            elif "AT&T" in desc:
                category = "Bills & utilities"
            elif "IKEA" in desc:
                category = "Home"
        
        if desc:
            cleaned.append((date, desc, amount, category))
    
    return cleaned

def detect_statement_type(text: str):
    """
    Detect the type of bank statement based on content markers.

    Returns:
        str: 'wells_fargo', 'chase', or 'unknown'
    """
    # Wells Fargo markers
    wells_fargo_markers = [
        "Wells Fargo",
        "BILT WORLD ELITE",
        "Posted Transactions",
        "Transaction Date Posting Date Description Amount"
    ]

    # Chase markers
    chase_markers = [
        "chase.com",
        "Sapphire Reserve",
        "Prime Visa",
        "Date Description Category Amount",
        "Printed from Chase Personal Online"
    ]

    wells_fargo_score = sum(1 for marker in wells_fargo_markers if marker in text)
    chase_score = sum(1 for marker in chase_markers if marker in text)

    if wells_fargo_score > chase_score and wells_fargo_score >= 2:
        return 'wells_fargo'
    elif chase_score > wells_fargo_score and chase_score >= 2:
        return 'chase'
    else:
        return 'unknown'

def get_credit_card_name(text: str, notion: any):
    """
    Extracts the credit card name from the statement header.
    Returns either 'Sapphire Reserve' or 'Amazon Chase' or 'BILT' or None if not found.
    """
    if "Sapphire Reserve" in text:
        print('Found Sapphire Reserve')
        return notion.credit_cards["SAPPHIRE"]
    elif "Prime Visa" in text:
        print('Found Prime Visa')
        return notion.credit_cards["AMAZON"]
    elif "BILT" in text:
        print('Found BILT')
        return notion.credit_cards["BILT"]
    print("No credit card found. Defaulting to Sapphire.")
    return notion.credit_cards["SAPPHIRE"]

def strip_to_statement__chase(text: str):
    """
    Chase statements have a header that says "Activity since last statement"
    This function strips the text to the statement after that header
    """
    markers = [
        "Activity since last statement",
        "Date Description Category Amount",
        "Date Description Amount",
        "Transactions"
    ]
    
    for marker in markers:
        if marker in text:
            parts = text.split(marker, 1)
            if len(parts) > 1:
                return marker + parts[1]
    
    return ""

def strip_to_statement__wells_fargo(text: str):
    """
    Wells Fargo statements have a header that says "Posted Transactions"
    This function strips the text to the statement after that header
    """
    # Look for the transaction section
    start_marker = "Posted Transactions"

    print(f"Looking for '{start_marker}' in text...")
    if start_marker in text:
        print("Found start marker!")
        start_pos = text.find(start_marker)
        print(f"Start position: {start_pos}")

        # For Wells Fargo, we want everything from "Posted Transactions" to the end
        # or until we find a clear end marker
        result = text[start_pos:].strip()

        # Look for potential end markers that come after the transactions
        end_markers = [
            "Account Summary",
            "Interest Charge Calculation",
            "Important Information",
            "Contact Us"
        ]

        for end_marker in end_markers:
            if end_marker in result:
                end_pos = result.find(end_marker)
                result = result[:end_pos].strip()
                print(f"Found end marker '{end_marker}', truncating at position {end_pos}")
                break

        print(f"Extracted text length: {len(result)}")
        return result

    print("Start marker not found")
    return ""

def extract_text__pdfplumber(file_path):
    """
    Extracts text from a PDF file using pdfplumber
    """
    with pdfplumber.open(file_path) as pdf:
        return "\n".join([page.extract_text() for page in pdf.pages if page.extract_text()])

def extract_text__pymupdf(file_path):
    """
    Extracts text from a PDF file using pymupdf
    """
    document = fitz.open(file_path)
    texts = ''
    for page_num in range(len(document)):
        page = document.load_page(page_num)
        text = page.get_text("text")
        texts += text
    document.close()
    print('texts: ', texts)
    return texts

def extract_text__pdfminer(file_path):
    """
    Extracts text from a PDF file using pdfminer.six with better layout preservation
    """
    output_string = StringIO()
    with open(file_path, 'rb') as in_file:
        parser = PDFParser(in_file)
        doc = PDFDocument(parser)
        rsrcmgr = PDFResourceManager()
        # Adjust LAParams for better table structure preservation
        device = TextConverter(rsrcmgr, output_string, laparams=LAParams(
            line_margin=0.5,  # Increased to better group related lines
            word_margin=0.1,
            boxes_flow=0.5,
            detect_vertical=True,
            all_texts=True,
            char_margin=2.0,  # Increased to better handle column spacing
            line_overlap=0.5,  # Added to better handle line spacing
        ))
        interpreter = PDFPageInterpreter(rsrcmgr, device)
        
        for page in PDFPage.create_pages(doc):
            interpreter.process_page(page)
            
    text = output_string.getvalue()
    output_string.close()
    
    # Debug print
    print("\nRaw text sample:")
    print(text[:200].replace('\n', '\\n'))
    
    return text

def get_files_in_dir(path: str): 
    files = []
    for file in os.listdir(path):
        if file.endswith(".pdf"):
            files.append(os.path.join(path, file))
    return files

def dynamic_date_parser(date: str):
    """
    Check date format and convert all dates to the format "YYYY-MM-DD"
    
    Args:
        date (str): Date string in one of these formats:
            - "MMM DD, YYYY" (e.g. "Feb 08, 2025")
            - "MM/DD/YY" (e.g. "04/01/25")
            - "YYYY-MM-DD" (e.g. "2025-04-01")
    
    Returns:
        str: Date in "YYYY-MM-DD" format
    """
    # If already in YYYY-MM-DD format, return as is
    if re.match(r"^\d{4}-\d{2}-\d{2}$", date):
        return date
    
    try:
        # Try MMM DD, YYYY format (e.g. "Feb 08, 2025")
        return datetime.strptime(date, "%b %d, %Y").strftime("%Y-%m-%d")
    except ValueError:
        try:
            # Try MM/DD/YY format (e.g. "04/01/25")
            return datetime.strptime(date, "%m/%d/%y").strftime("%Y-%m-%d")
        except ValueError:
            raise ValueError(f"Unsupported date format: {date}. Must be one of: 'MMM DD, YYYY', 'MM/DD/YY', or 'YYYY-MM-DD'")

def parse_statement__wells_fargo(statement_text: str):
    """
    Parses a Wells Fargo/BILT statement text into a list of transactions with categories.
    Handles the new browser format with Transaction Date and Posting Date columns.
    """
    lines = statement_text.split("\n")
    transactions = []
    date_pattern = re.compile(r"\b\d{2}/\d{2}/\d{2}\b")  # MM/DD/YY format
    amount_pattern = re.compile(r"[+\-]?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?")

    # Find where the transaction table starts
    table_start = -1
    for i, line in enumerate(lines):
        if "Posted Transactions" in line:
            table_start = i
            break

    if table_start == -1:
        return []

    # Process the lines after the header
    i = table_start + 1
    current_transaction_date = None
    current_posting_date = None
    current_desc = []
    current_amount = None

    while i < len(lines):
        line = lines[i].strip()

        # Skip empty lines and navigation elements
        if not line or "First Previous Next" in line:
            i += 1
            continue

        # Look for lines that start with a date pattern (transaction lines)
        date_matches = list(date_pattern.finditer(line))

        if len(date_matches) >= 2:
            # This line has both transaction date and posting date
            # Save previous transaction if complete
            if current_transaction_date and current_amount and current_desc:
                desc = " ".join(current_desc).strip()
                if desc:
                    transactions.append((
                        current_transaction_date,
                        desc,
                        current_amount,
                        "—"  # Wells Fargo doesn't provide categories
                    ))

            # Start new transaction
            current_transaction_date = date_matches[0].group()
            current_posting_date = date_matches[1].group()
            current_desc = []
            current_amount = None

            # Process rest of line after the two dates
            rest = line[date_matches[1].end():].strip()
            if rest:
                # Look for amount
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    desc = rest[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                else:
                    # No amount found, add to description
                    current_desc.append(rest)

        elif len(date_matches) == 1 and not current_transaction_date:
            # Single date at start of line (shouldn't happen in new format, but handle it)
            current_transaction_date = date_matches[0].group()
            current_posting_date = current_transaction_date
            current_desc = []
            current_amount = None

            rest = line[date_matches[0].end():].strip()
            if rest:
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    desc = rest[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                else:
                    current_desc.append(rest)

        else:
            # Line doesn't start with date pattern
            if current_transaction_date:  # We're in a transaction
                # Check for amount if we don't have one yet
                amount_match = amount_pattern.search(line)
                if amount_match and not current_amount:
                    current_amount = amount_match.group()
                    desc = line[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                # Skip reference numbers (lines starting with #)
                elif line.startswith('#'):
                    pass  # Skip reference numbers
                # Add to description if it's not empty and not a reference
                elif line and not line.startswith('XXXXXX'):
                    current_desc.append(line)

        i += 1

    # Add final transaction if complete
    if current_transaction_date and current_amount and current_desc:
        desc = " ".join(current_desc).strip()
        if desc:
            transactions.append((
                current_transaction_date,
                desc,
                current_amount,
                "—"  # Wells Fargo doesn't provide categories
            ))

    # Clean up transactions
    cleaned = []
    for date, desc, amount, category in transactions:
        # Clean description
        desc = re.sub(r'\s+', ' ', desc).strip()

        # Remove common noise from descriptions
        desc = desc.replace("(pending)", "").strip()

        # Infer categories based on merchant names
        if "BPS*BILT RENT" in desc:
            category = "Bills & utilities"
        elif any(food in desc.upper() for food in ["CARNICERIA", "NAYAX", "STARBUCKS"]):
            category = "Food & drink"
        elif "CA DMV" in desc:
            category = "Bills & utilities"
        elif "ONLINE PYMT" in desc or "PAYMENT THANK YOU" in desc:
            category = "—"  # Keep payments uncategorized

        if desc:
            cleaned.append((date, desc, amount, category))

    return cleaned

def parse_statement__chase(statement_text: str):
    """
    Parse Chase statements from new browser format into a list of transactions with categories.
    Handles both pending and posted transactions.
    """
    lines = statement_text.split("\n")
    transactions = []
    date_pattern = re.compile(r"\b\w{3} \d{1,2}, \d{4}\b")  # Jul 16, 2025
    amount_pattern = re.compile(r"-?\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?")
    categories = ["Shopping", "Food & drink", "Groceries", "Bills & utilities", "Home", "Travel", "—"]

    # Look for transaction sections
    in_transactions = False
    current_date = None
    current_desc = []
    current_amount = None
    current_category = None

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # Skip empty lines and navigation elements
        if not line or "of 5" in line or "chase.com" in line or "Printed from Chase" in line:
            i += 1
            continue

        # Check if we're entering a transaction section
        if ("Date Description Amount" in line or
            "Date Description Category Amount" in line):
            in_transactions = True
            i += 1
            continue

        if not in_transactions:
            i += 1
            continue

        # Check for date at start of line
        date_match = date_pattern.search(line)
        if date_match and line.startswith(date_match.group()):
            # Save previous transaction if complete
            if current_date and current_amount and current_desc:
                desc = " ".join(current_desc).strip()
                if desc:
                    transactions.append((
                        current_date,
                        desc,
                        current_amount,
                        current_category or "—"
                    ))

            # Start new transaction
            current_date = date_match.group()
            current_desc = []
            current_amount = None
            current_category = None

            # Process rest of line after date
            rest = line[date_match.end():].strip()
            if rest:
                # Look for amount in the same line
                amount_match = amount_pattern.search(rest)
                if amount_match:
                    current_amount = amount_match.group()
                    # Everything before amount is description/category
                    desc_and_cat = rest[:amount_match.start()].strip()

                    # Check if any category is at the end
                    found_category = None
                    for cat in categories:
                        if desc_and_cat.endswith(cat):
                            found_category = cat
                            desc_and_cat = desc_and_cat[:-len(cat)].strip()
                            break

                    current_category = found_category
                    if desc_and_cat:
                        current_desc.append(desc_and_cat)
                else:
                    # No amount in this line, add to description
                    current_desc.append(rest)
        else:
            # Line doesn't start with date
            if current_date:  # We're in a transaction
                # Check for amount
                amount_match = amount_pattern.search(line)
                if amount_match and not current_amount:
                    current_amount = amount_match.group()
                    desc = line[:amount_match.start()].strip()
                    if desc:
                        current_desc.append(desc)
                # Check if line is a category
                elif line in categories:
                    current_category = line
                # Check for special payment lines
                elif "Payment Thank You" in line:
                    if current_desc:
                        current_desc.append(line)
                    else:
                        current_desc = [line]
                    current_category = "—"
                # Skip navigation and page elements
                elif line in ["Showing Year to date"] or "of 5" in line or "chase.com" in line:
                    pass
                # Otherwise add to description
                else:
                    if line:
                        current_desc.append(line)

        i += 1

    # Add final transaction if complete
    if current_date and current_amount and current_desc:
        desc = " ".join(current_desc).strip()
        if desc:
            transactions.append((
                current_date,
                desc,
                current_amount,
                current_category or "—"
            ))

    # Clean up transactions
    cleaned = []
    for date, desc, amount, category in transactions:
        # Clean description
        desc = re.sub(r'\s+', ' ', desc).strip()

        # Remove category text if it appears in description
        for cat in categories:
            if desc.endswith(cat):
                desc = desc[:-len(cat)].strip()

        # Remove duplicate merchant names and clean up
        words = desc.split()
        # Remove consecutive duplicate words (common in Chase statements)
        cleaned_words = []
        prev_word = None
        for word in words:
            if word != prev_word:
                cleaned_words.append(word)
            prev_word = word
        desc = ' '.join(cleaned_words)

        # Remove amount references that got mixed into description
        desc = re.sub(r'\$\d+\.\d{2}', '', desc).strip()

        # Remove category names that got mixed into description
        for cat in categories:
            desc = desc.replace(cat, '').strip()

        # Remove common noise patterns
        desc = re.sub(r'\s+', ' ', desc)  # Multiple spaces
        desc = re.sub(r'—\s*−', '', desc)  # Payment symbols
        desc = desc.replace('Payment Thank You - Web', 'Payment Thank You').strip()

        # Handle special cases for category inference
        if category == "—":
            if "Costco" in desc:
                category = "Shopping"
            elif "DoorDash" in desc or "Starbucks" in desc or "Carl's Jr" in desc:
                category = "Food & drink"
            elif "Zion Market" in desc:
                category = "Groceries"
            elif "AT&T" in desc:
                category = "Bills & utilities"
            elif "Lyft" in desc or "LA METRO" in desc:
                category = "Travel"

        if desc:
            cleaned.append((date, desc, amount, category))

    return cleaned

def parse_statement(statement_text: str):
    """
    Unified statement parser that detects the statement type and routes to the appropriate parser.

    Args:
        statement_text (str): The extracted statement text

    Returns:
        list: List of transactions as tuples (date, description, amount, category)
    """
    statement_type = detect_statement_type(statement_text)

    if statement_type == 'wells_fargo':
        print("Detected Wells Fargo statement")
        return parse_statement__wells_fargo(statement_text)
    elif statement_type == 'chase':
        print("Detected Chase statement")
        return parse_statement__chase(statement_text)
    else:
        print(f"Unknown statement type. Trying Chase parser as fallback.")
        return parse_statement__chase(statement_text)

def strip_to_statement(text: str):
    """
    Unified function to strip text to the statement section based on detected type.

    Args:
        text (str): Full PDF text

    Returns:
        str: Statement section text
    """
    statement_type = detect_statement_type(text)

    if statement_type == 'wells_fargo':
        print("Using Wells Fargo text stripping")
        return strip_to_statement__wells_fargo(text)
    elif statement_type == 'chase':
        print("Using Chase text stripping")
        return strip_to_statement__chase(text)
    else:
        print("Unknown statement type. Trying Chase stripping as fallback.")
        return strip_to_statement__chase(text)
